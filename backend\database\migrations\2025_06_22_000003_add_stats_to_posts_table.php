<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('posts', function (Blueprint $table) {
            if (!Schema::hasColumn('posts', 'likes')) {
                $table->integer('likes')->default(0)->after('map_url');
            }
            if (!Schema::hasColumn('posts', 'views')) {
                $table->integer('views')->default(0)->after('likes');
            }
            if (!Schema::hasColumn('posts', 'titre')) {
                $table->string('titre')->nullable()->after('views');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('posts', function (Blueprint $table) {
            if (Schema::hasColumn('posts', 'likes')) {
                $table->dropColumn('likes');
            }
            if (Schema::hasColumn('posts', 'views')) {
                $table->dropColumn('views');
            }
            if (Schema::hasColumn('posts', 'titre')) {
                $table->dropColumn('titre');
            }
        });
    }
};
