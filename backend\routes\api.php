<?php

use App\Http\Controllers\AuthController;
use App\Http\Controllers\PostController;
use App\Http\Controllers\ProfileController;
use App\Http\Controllers\TagController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;


// Public routes
Route::post('/login', [AuthController::class, 'apiLogin']);
Route::get('/', function(){
    return "hell";
});
Route::post('/register', [AuthController::class, 'apiRegister']);

// Protected routes
Route::middleware('auth:sanctum')->group(function () {
    Route::post('/logout', [AuthController::class, 'apiLogout']);
    Route::get('/user', [AuthController::class, 'getUser']);
    
    // Profile routes
    Route::get('/profile', [ProfileController::class, 'getProfile']);
    Route::post('/profile/update', [ProfileController::class, 'updateProfile']);
    Route::get('/profile/saved-posts', [ProfileController::class, 'getSavedPosts']);
    Route::post('/posts/{id}/toggle-save', [ProfileController::class, 'toggleSavePost']);
    
    // Post routes
    Route::get('/posts', [PostController::class, 'index']);
    Route::get('/post', [PostController::class, 'create']);
    Route::post('/posts', [PostController::class, 'store']);
    Route::get('/posts/{id}', [PostController::class, 'show']);
    Route::get('/editposts/{id}', [PostController::class, 'edit']);
    Route::post('/posts/{id}', [PostController::class, 'update']);
    Route::delete('/posts/{id}', [PostController::class, 'destroy']);

    // Tag routes
    Route::get('/tags', [TagController::class, 'index']);
    Route::get('/tags/trending', [TagController::class, 'trending']);
    Route::get('/tags/search', [TagController::class, 'search']);
    Route::get('/tags/{id}', [TagController::class, 'show']);
});


