#addpostlabel,
.img_post {
    width: 40px;
    aspect-ratio: 1/1;
    height: 40px;
}

#addpostlabel img {
    width: 100%;
    height: auto;
    border-radius: 50%;
}

.custom-textarea:active,
.custom-textarea:focus {
    background-color: transparent;
    outline: 0px;
}

.custum-mini-card {
    display: flex;
    justify-content: center;
    gap: 15px;
    height: 25px;
    cursor: pointer;
    padding: 0 10px;
    font-size: 11px;
    margin-top: 3px;
    border-radius: 5px;
    align-items: center;
    background: #E4E6EB;
    justify-content: space-between;
}

img {
    cursor: pointer;
}

.post {
    transition: margin-left 0.18s ease;
}

.active .post {
    margin-left: -500px;
}

.post header {
    font-size: 22px;
    font-weight: 600;
    padding: 17px 0;
    text-align: center;
    border-bottom: 1px solid #bfbfbf;
}

.post form {
    padding: 20px 25px;
}

.post form .content,
.audience .list li .column {
    display: flex;
    align-items: center;
}

.post form .content img {
    cursor: default;
    max-width: 52px;
}

.post form .content .details {
    margin: -3px 0 0 12px;
}

form .content .details p {
    font-size: 17px;
    font-weight: 500;
}

.content .details .privacy {
    display: flex;
    justify-content: center;
    gap: 15px;
    height: 25px;
    cursor: pointer;
    padding: 0 10px;
    font-size: 11px;
    margin-top: 3px;
    border-radius: 5px;
    align-items: center;
    background: #E4E6EB;
    justify-content: space-between;
}

.privacy span {
    font-size: 13px;
    margin-top: 1px;
    font-weight: 500;
}

.details .privacy i:last-child {
    font-size: 13px;
    margin-left: 1px;
}

form :where(textarea, input[type="submit"]) {
    width: 100%;
    outline: none;
    border: none;
}

form textarea {
    resize: none;
    font-size: 18px;
    margin-top: 30px;
    min-height: 100px;
}

form textarea::placeholder {
    color: #858585;
}

form textarea:focus::placeholder {
    color: #b3b3b3;
}

form :where(.theme-emoji, .options) {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.theme-emoji img:last-child {
    max-width: 24px;
}

form .options {
    height: 57px;
    margin: 15px 0;
    padding: 0 15px;
    border-radius: 7px;
    border: 1px solid #B9B9B9;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

form .options :where(.list, li),
.audience :where(.arrow-back, .icon, li .radio) {
    display: flex;
    align-items: center;
    justify-content: center;
}

form .options p {
    color: #595959;
    font-size: 15px;
    font-weight: 500;
    cursor: default;
}

form .options .list {
    list-style: none;
}

.options .list li {
    height: 42px;
    width: 42px;
    margin: 0 -1px;
    cursor: pointer;
    border-radius: 50%;
}

.options .list li:hover {
    background: #f0f1f4;
}

.options .list li img {
    width: 23px;
}

form input[type="submit"] {
    height: 53px;
    color: #fff;
    font-size: 18px;
    font-weight: 500;
    cursor: pointer;
    color: #BCC0C4;
    cursor: no-drop;
    border-radius: 7px;
    background: #e2e5e9;
    transition: all 0.3s ease;
}

form textarea:valid~input[type="submit"] {
    color: #fff;
    cursor: pointer;
    background: #4599FF;
}

form textarea:not(:valid)~input[type="submit"] {
    cursor: not-allowed;
}

form textarea:valid~input[type="submit"]:hover {
    background: #1a81ff;
}

.audience {
    opacity: 0;
    transition: opacity 0.12s ease;
}

.active .audience {
    opacity: 1;
}

.audience header {
    padding: 17px 0;
    text-align: center;
    position: relative;
    border-bottom: 1px solid #bfbfbf;
}

.audience header .arrow-back {
    position: absolute;
    left: 25px;
    width: 35px;
    height: 35px;
    cursor: pointer;
    font-size: 15px;
    color: #747474;
    border-radius: 50%;
    background: #E4E6EB;
}

.audience header p {
    font-size: 22px;
    font-weight: 600;
}

.audience .content {
    margin: 20px 25px 0;
}

.audience .content p {
    font-size: 17px;
    font-weight: 500;
}

.audience .content span {
    font-size: 14px;
    color: #65676B;
}

.audience .list {
    margin: 15px 16px 20px;
    list-style: none;
}

.audience .list li {
    display: flex;
    cursor: pointer;
    margin-bottom: 5px;
    padding: 12px 10px;
    border-radius: 7px;
    align-items: center;
    justify-content: space-between;
}

.list li.active,
.audience .list li.active:hover {
    background: #E5F1FF;
}

.audience .list li:hover {
    background: #f0f1f4;
}

.audience .list li .column .icon {
    height: 50px;
    width: 50px;
    color: #333;
    font-size: 23px;
    border-radius: 50%;
    background: #E4E6EB;
}

.audience .list li.active .column .icon {
    background: #cce4ff;
}

.audience .list li .column .details {
    margin-left: 15px;
}

.list li .column .details p {
    font-weight: 500;
}

.list li .column .details span {
    font-size: 14px;
    color: #65676B;
}

.list li .radio {
    height: 20px;
    width: 20px;
    border-radius: 50%;
    position: relative;
    border: 1px solid #707070;
}

.list li.active .radio {
    border: 2px solid #4599FF;
}

.list li .radio::before {
    content: "";
    width: 12px;
    height: 12px;
    border-radius: inherit;
}

.list li.active .radio::before {
    background: #4599FF;
}

.anime {
    transition: all 0.3s ease;
}

.carousel-inner * {
    max-height: 500px !important;
    object-fit: cover;
}

.custom_delete {
    position: absolute !important;
    left: 125%;
    top: 30%;
    transform: translateY(-50%);
    cursor: pointer;
    transition: all 1s ease;
}

.custom_quit {
    position: absolute !important;
    left: 125%;
    top: 60%;
    transform: translateY(-50%);
    cursor: pointer;
    transition: all 1s ease;
}



@media screen and (max-width: 400px) {
    .privacy span {
        font-size: 10px !important;
    }
}

@media screen and (max-width: 900px) {
    .custom_delete {
        position: absolute !important;
        left: 75%;
        top: 100%;
        transform: translate(-50%, -50%) !important;
        cursor: pointer;
        transition: all 1s ease;
    }

    .custom_quit {
        position: absolute !important;
        left: 25%;
        top: 100%;
        transform: translate(-50%, -50%) !important;
        cursor: pointer;
        transition: all 1s ease;
    }
}

@media screen and (min-width: 900px) and (max-width: 1300px) {
    .custom_delete {
        position: absolute !important;
        left: 75%;
        top: 100%;
        transform: translate(-50%, -50%) !important;
        cursor: pointer;
        transition: all 1s ease;
    }

    .custom_quit {
        position: absolute !important;
        left: 25%;
        top: 100%;
        transform: translate(-50%, -50%) !important;
        cursor: pointer;
        transition: all 1s ease;
    }
}