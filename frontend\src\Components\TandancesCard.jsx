import React from 'react';
import { FaHashtag } from "react-icons/fa";
import { IoTrendingUp } from "react-icons/io5";
import { useNavigate } from 'react-router-dom';

function TandancesCard({ el, onClick }) {
    const navigate = useNavigate();

    const handleClick = () => {
        if (onClick) {
            onClick(el);
        } else {
            // Navigate to discover page with tag filter
            navigate(`/discover?tag=${encodeURIComponent(el.title || el.tag)}`);
        }
    };

    // Support both old format (title/count) and new format (tag/posts_count)
    const tagName = el.title || el.tag;
    const postCount = el.count || el.posts_count || el.recent_posts_count || 0;
    const isTrending = el.trending !== undefined ? el.trending : true; // Default to true for trending tags

    return (
        <div
            className='group hover:bg-gray-50 transition-all duration-300 rounded-xl p-3 cursor-pointer'
            onClick={handleClick}
        >
            <div className='flex items-center gap-3'>
                <div className='flex-shrink-0'>
                    <div className='w-10 h-10 flex items-center justify-center rounded-full bg-primary/10 group-hover:bg-primary/20 transition-colors duration-300'>
                        <IoTrendingUp className='text-secondary text-xl' />
                    </div>
                </div>
                <div className='flex-grow min-w-0'>
                    <div className='flex items-center gap-1'>
                        <FaHashtag className='text-secondary text-sm' />
                        <h3 className='text-gray-900 font-medium truncate'>{tagName}</h3>
                    </div>
                    <div className='flex items-center gap-2 mt-0.5'>
                        <span className='text-sm text-gray-500'>{postCount.toLocaleString()} posts</span>
                        {isTrending && (
                            <span className='inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-primary/10 text-secondary'>
                                Trending
                            </span>
                        )}
                        {el.trending_score && (
                            <span className='text-xs text-gray-400'>
                                Score: {Math.round(el.trending_score)}
                            </span>
                        )}
                    </div>
                </div>
            </div>
        </div>
    );
}

export default TandancesCard
