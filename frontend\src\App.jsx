import { Route, Routes, Navigate } from 'react-router-dom';
import Connexion from './Pages/Connexion';
import Home from './Pages/Home';
import Profile from './Pages/Profile';
import PostDetail from './Pages/PostDetail';
import Discover from './Pages/Discover';
import Navbar from './Components/Navbar';
import './App.css'
import { useSelector } from 'react-redux';
import CreatePost from './Pages/CreatePost';

// Protected route component
const ProtectedRoute = ({ children }) => {
  const userConnected = useSelector((state) => state.userConnected);
  
  if (!userConnected) {
    return <Navigate to="/" replace />;
  }
  
  return children;
};

const App = () => {
  const userConnected = useSelector((state) => state.userConnected);

  if (!userConnected) {
    return (
      <>
        <Routes>
          <Route path='/' element={<Connexion />} />
          <Route path='*' element={<Navigate to="/" replace />} />
        </Routes>
      </>
    )
  }
  
  return (
    <div className='flex'>
      <Navbar />
      <main className='ml-72 flex-1 min-h-screen'>
        <Routes>
          <Route path='/' element={<Home />} />
          <Route path='/create-post' element={<CreatePost />} />
          <Route path='/profile' element={<Profile />} />
          <Route path='/discover' element={<Discover />} />
          <Route path="/post/:postId" element={<PostDetail />} />
          <Route path='*' element={<Navigate to="/" replace />} />
        </Routes>
      </main>
    </div>
  )
}

export default App;

