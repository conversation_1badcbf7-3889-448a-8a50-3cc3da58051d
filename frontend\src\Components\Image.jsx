import React from 'react';

const Image = ({ 
    src, 
    fallback = '', 
    alt = '', 
    storagePrefix = true, 
    ...props 
}) => {
    const baseUrl = import.meta.env.VITE_API_URL;
    const storageUrl = import.meta.env.VITE_STORAGE_URL;

    let imageUrl = src;

    if (src?.startsWith('http')) {
        imageUrl = src;
    } else if (storagePrefix && src) {
        imageUrl = `${storageUrl}/${src}`;
    } else if (src) {
        imageUrl = `${baseUrl}${src}`;
    }

    const handleError = (e) => {
        const img = e.target;
        if (fallback) {
            img.src = fallback;
        }
    };

    return (
        <img 
            src={imageUrl} 
            alt={alt}
            onError={handleError}
            {...props}
        />
    );
};

export default Image;