<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Post extends Model
{
    protected $fillable = [
        'description',
        'location',
        'map_url',
        'user_id',
        'likes',
        'views',
        'titre'
    ];
    public function images()
        {
            return $this->hasMany(Image::class);
        }
    public function tags()
        {
            return $this->belongsToMany(Tag::class);
        }
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function comments()
    {
        return $this->hasMany(Comment::class);
    }

    public function likes()
    {
        return $this->hasMany(Like::class);
    }

    public function likedBy()
    {
        return $this->belongsToMany(User::class, 'likes')->withTimestamps();
    }

    // Get comments count
    public function getCommentsCountAttribute()
    {
        return $this->comments()->count();
    }

    // Increment views
    public function incrementViews()
    {
        $this->increment('views');
    }

    // Increment likes
    public function incrementLikes()
    {
        $this->increment('likes');
    }

    // Decrement likes
    public function decrementLikes()
    {
        $this->decrement('likes');
    }
}





