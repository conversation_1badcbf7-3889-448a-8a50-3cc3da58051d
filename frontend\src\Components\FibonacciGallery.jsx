import React, { useState } from "react";
import { FaPlus } from "react-icons/fa";
import Image from "./image";

const FibonacciGallery = ({ images }) => {
  const [showModal, setShowModal] = useState(false);
  const [currentSlide, setCurrentSlide] = useState(0);
  const handleModalToggle = () => setShowModal(!showModal);

  const nextSlide = () => {
    setCurrentSlide((prev) => (prev + 1) % images.length);
  };

  const prevSlide = () => {
    setCurrentSlide((prev) => (prev - 1 + images.length) % images.length);
  };

  const getLayout = () => {
    switch (images.length) {
      case 1:
        return (
          <div className="flex flex-col">
            <div className="w-full">
              <Image 
                src={images[0].image} 
                alt="Gallery" 
                className="w-full h-auto object-cover rounded-lg" 
              />
            </div>
          </div>
        );
      case 2:
        return (
          <div className="grid grid-cols-2 gap-2">
            <div>
              <Image 
                src={images[0].image} 
                alt="Gallery" 
                className="w-full h-full object-cover rounded-lg" 
              />
            </div>
            <div>
              <Image 
                src={images[1].image} 
                alt="Gallery" 
                className="w-full h-full object-cover rounded-lg" 
              />
            </div>
          </div>
        );
      case 3:
        return (
          <div className="grid grid-cols-2 gap-2">
            <div className="h-full">
              <Image 
                src={images[0].image} 
                alt="Gallery" 
                className="w-full h-full object-cover rounded-lg" 
              />
            </div>
            <div className="flex flex-col gap-2">
              <div>
                <Image 
                  src={images[1].image} 
                  alt="Gallery" 
                  className="w-full h-full object-cover rounded-lg" 
                />
              </div>
              <div>
                <Image 
                  src={images[2].image} 
                  alt="Gallery" 
                  className="w-full h-full object-cover rounded-lg" 
                />
              </div>
            </div>
          </div>
        );
      default:
        return (
          <div className="grid grid-cols-2 gap-2">
            <div className="h-full">
              <Image 
                src={images[0].image} 
                alt="Gallery" 
                className="w-full h-full object-cover rounded-lg" 
              />
            </div>
            <div className="flex flex-col gap-2">
              <div>
                <Image 
                  src={images[1].image} 
                  alt="Gallery" 
                  className="w-full h-full object-cover rounded-lg" 
                />
              </div>
              <div className="grid grid-cols-2 gap-2">
                <div>
                  <Image 
                    src={images[2].image} 
                    alt="Gallery" 
                    className="w-full h-full object-cover rounded-lg" 
                  />
                </div>
                <div className="relative">
                  <Image
                    src={images[3].image}
                    alt="Gallery"
                    className="w-full h-full object-cover rounded-lg"
                  />
                  {/* Overlay for more images */}
                  <div
                    onClick={handleModalToggle}
                    className="absolute inset-0 flex items-center justify-center bg-black/50 rounded-lg cursor-pointer"
                  >
                    <FaPlus className="w-8 h-8 text-white" />
                  </div>
                </div>
              </div>
            </div>
          </div>
        );
    }
  };

  return (
    <>
      {getLayout()}

      {/* Modal using Tailwind */}
      {showModal && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50">
          <div className="bg-white rounded-lg w-11/12 max-w-4xl">
            <div className="flex items-center justify-between p-4 border-b">
              <h5 className="text-xl font-semibold">Gallery</h5>
              <button
                onClick={handleModalToggle}
                className="p-1 hover:bg-gray-100 rounded-full"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            <div className="p-4">
              <div className="relative">
                {/* Carousel */}
                <div className="relative">
                  <div className="flex transition-transform duration-300">
                    {images.map((media, index) => (
                      <div 
                        key={index} 
                        className={`w-full flex-shrink-0 transition-opacity duration-300 ${
                          index === currentSlide ? 'block' : 'hidden'
                        }`}
                      >
                        <Image 
                          src={media.image} 
                          className="w-full h-[400px] object-cover" 
                          alt="Gallery item" 
                        />
                      </div>
                    ))}
                  </div>
                  
                  {/* Navigation buttons */}
                  <button 
                    className="absolute left-0 top-1/2 -translate-y-1/2 bg-white/80 p-2 rounded-r-lg hover:bg-white"
                    onClick={prevSlide}
                  >
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 19l-7-7 7-7" />
                    </svg>
                  </button>
                  
                  <button 
                    className="absolute right-0 top-1/2 -translate-y-1/2 bg-white/80 p-2 rounded-l-lg hover:bg-white"
                    onClick={nextSlide}
                  >
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7" />
                    </svg>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default FibonacciGallery;
