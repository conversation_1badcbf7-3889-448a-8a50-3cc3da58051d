import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { Provider } from 'react-redux';
import { <PERSON><PERSON>erRouter } from 'react-router-dom';
import { configureStore } from '@reduxjs/toolkit';
import Profile from './Profile';
import api from '../services/api';

// Mock the API
jest.mock('../services/api');
const mockedApi = api;

// Mock the components
jest.mock('../Components/UserNav', () => {
    return function UserNav() {
        return <div data-testid="user-nav">UserNav</div>;
    };
});

jest.mock('../Components/Post', () => {
    return function Post({ post }) {
        return <div data-testid="post">{post.description}</div>;
    };
});

jest.mock('../Components/image', () => {
    return function Image({ src, alt, className }) {
        return <img src={src} alt={alt} className={className} data-testid="image" />;
    };
});

// Create a mock store
const createMockStore = (initialState = {}) => {
    return configureStore({
        reducer: {
            root: (state = {
                userConnected: true,
                user: {
                    id: 1,
                    name: 'Test User',
                    username: 'testuser',
                    email: '<EMAIL>',
                    profil: '/test-avatar.jpg',
                    cover: '/test-cover.jpg',
                    bio: 'Test bio',
                    location: 'Test Location'
                },
                ...initialState
            }) => state
        }
    });
};

const renderWithProviders = (component, { initialState = {} } = {}) => {
    const store = createMockStore(initialState);
    return render(
        <Provider store={store}>
            <BrowserRouter>
                {component}
            </BrowserRouter>
        </Provider>
    );
};

describe('Profile Component', () => {
    beforeEach(() => {
        // Reset mocks
        jest.clearAllMocks();
        
        // Mock API responses
        mockedApi.get.mockImplementation((url) => {
            if (url === '/profile') {
                return Promise.resolve({
                    data: {
                        id: 1,
                        nom: 'Test',
                        prenom: 'User',
                        username: 'testuser',
                        email: '<EMAIL>',
                        profil: '/test-avatar.jpg',
                        cover_image: '/test-cover.jpg',
                        bio: 'Test bio',
                        location: 'Test Location'
                    }
                });
            }
            if (url === '/posts') {
                return Promise.resolve({
                    data: [
                        {
                            id: 1,
                            description: 'Test post',
                            user_id: 1,
                            user: { id: 1, name: 'Test User' },
                            images: [{ image: '/test-image.jpg' }],
                            likes: 10,
                            comments: []
                        }
                    ]
                });
            }
            return Promise.resolve({ data: [] });
        });
    });

    test('renders profile component', async () => {
        renderWithProviders(<Profile />);
        
        expect(screen.getByTestId('user-nav')).toBeInTheDocument();
        
        // Wait for profile data to load
        await waitFor(() => {
            expect(screen.getByText('Test User')).toBeInTheDocument();
        });
    });

    test('displays profile stats', async () => {
        renderWithProviders(<Profile />);
        
        await waitFor(() => {
            expect(screen.getByText('Posts')).toBeInTheDocument();
            expect(screen.getByText('Followers')).toBeInTheDocument();
            expect(screen.getByText('Following')).toBeInTheDocument();
        });
    });

    test('switches between tabs', async () => {
        renderWithProviders(<Profile />);
        
        await waitFor(() => {
            expect(screen.getByText('Posts')).toBeInTheDocument();
        });

        // Click on Saved tab
        const savedTab = screen.getByText('Saved');
        fireEvent.click(savedTab);
        
        expect(screen.getByText('No saved posts yet')).toBeInTheDocument();
    });

    test('opens edit profile modal', async () => {
        renderWithProviders(<Profile />);
        
        await waitFor(() => {
            const editButton = screen.getByText('Edit Profile');
            fireEvent.click(editButton);
        });
        
        expect(screen.getByText('Edit Profile')).toBeInTheDocument();
        expect(screen.getByPlaceholderText('Enter your first name')).toBeInTheDocument();
    });

    test('handles API errors gracefully', async () => {
        mockedApi.get.mockRejectedValue(new Error('API Error'));
        
        renderWithProviders(<Profile />);
        
        await waitFor(() => {
            expect(screen.getByText(/Failed to load profile data/)).toBeInTheDocument();
        });
    });
});
