import React, { useState } from 'react'
import { FaRegBell } from "react-icons/fa"
import { IoPersonCircleOutline } from 'react-icons/io5'
import { FiLogOut, FiSettings } from "react-icons/fi"
import { useDispatch, useSelector } from 'react-redux'
import { logoutSuccess } from '../Reducer/Actions'
import useClickOutside from '../hooks/useClickOutside'
import { toast } from 'react-toastify'
import api from '../services/api'
import Image from './image'

function UserNav() {
    const dispatch = useDispatch()
    const user = useSelector((state) => state.user)
    const [notifications, setNotifications] = useState(false)
    const [userOpt, setUserOpt] = useState(false)

    // Refs for click outside detection
    const notificationsRef = useClickOutside(() => setNotifications(false))
    const userOptRef = useClickOutside(() => setUserOpt(false))

    const handleLogout = async () => {
        try {
            // Call the logout endpoint directly
            await api.post('/logout');
            
            // Remove token and user from localStorage
            localStorage.removeItem('token');
            localStorage.removeItem('user');
            
            // Dispatch simple logout action
            dispatch(logoutSuccess());
            
            toast.info('You have been logged out');
        } catch (error) {
            console.error('Logout failed:', error);
            
            // Even if API call fails, still log out locally
            localStorage.removeItem('token');
            localStorage.removeItem('user');
            dispatch(logoutSuccess());
            
            toast.info('You have been logged out');
        }
    };

    return (
        <nav className="sticky top-0 z-50 w-full bg-white/80 backdrop-blur-md border-b border-gray-100">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div className="flex items-center justify-between h-16">
                    {/* Left side - Search Bar */}
                    <div className="flex-1 max-w-md">
                        <div className="relative">
                            <input
                                type="text"
                                placeholder="Search..."
                                className="w-full pl-4 pr-10 py-2 rounded-full bg-gray-50 border-none focus:ring-2 focus:ring-primary/50 focus:outline-none"
                            />
                            <div className="absolute right-3 top-2.5 text-gray-400">
                                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                                </svg>
                            </div>
                        </div>
                    </div>

                    {/* Right side - User Actions */}
                    <div className="flex items-center space-x-4">
                        {/* Notifications */}
                        <div className="relative" ref={notificationsRef}>
                            <button
                                onClick={() => {
                                    setNotifications(!notifications)
                                    setUserOpt(false) // Close other dropdown
                                }}
                                className="p-2 text-gray-500 hover:text-gray-700 focus:outline-none"
                            >
                                <div className="relative">
                                    <FaRegBell className="w-6 h-6" />
                                    <span className="absolute -top-1 -right-1 w-4 h-4 bg-red-500 text-white text-xs rounded-full flex items-center justify-center">
                                        3
                                    </span>
                                </div>
                            </button>

                            {notifications && (
                                <div className="absolute right-0 mt-3 w-80 overflow-hidden bg-white rounded-xl shadow-lg ring-1 ring-black ring-opacity-5">
                                    <div className="p-4">
                                        <h3 className="text-sm font-semibold text-gray-900 pb-2 border-b">
                                            Notifications
                                        </h3>
                                        <div className="mt-2 space-y-2">
                                            <div className="flex items-start p-2 hover:bg-gray-50 rounded-lg transition">
                                                <div className="flex-shrink-0">
                                                    <img className="h-8 w-8 rounded-full" src={user.profilePic} alt="" />
                                                </div>
                                                <div className="ml-3">
                                                    <p className="text-sm text-gray-700">
                                                        <span className="font-medium">Sarah</span> liked your post
                                                    </p>
                                                    <p className="text-xs text-gray-500">2 min ago</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            )}
                        </div>

                        {/* User Profile */}
                        <div className="relative" ref={userOptRef}>
                            <button
                                onClick={() => {
                                    setUserOpt(!userOpt)
                                    setNotifications(false) // Close other dropdown
                                }}
                                className="flex items-center space-x-3 p-1.5 rounded-full hover:bg-gray-100 transition-colors duration-200"
                            >
                                <Image 
                                    src={user.profil} 
                                    alt={user.username}
                                    className="w-8 h-8 rounded-full object-cover ring-2 ring-primary/30"
                                />
                            </button>

                            {userOpt && (
                                <div className="absolute right-0 mt-2 w-56 rounded-xl bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none">
                                    {/* User Info */}
                                    <div className="p-4 border-b">
                                        <p className="text-sm font-medium text-gray-900">{user.name}</p>
                                        <p className="text-xs text-gray-500">{user.email}</p>
                                    </div>

                                    {/* Menu Items */}
                                    <div className="py-1">
                                        <a
                                            href="#"
                                            className="group flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-50"
                                        >
                                            <IoPersonCircleOutline className="mr-3 h-5 w-5 text-gray-400 group-hover:text-gray-500" />
                                            Profile
                                        </a>
                                        <a
                                            href="#"
                                            className="group flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-50"
                                        >
                                            <FiSettings className="mr-3 h-5 w-5 text-gray-400 group-hover:text-gray-500" />
                                            Settings
                                        </a>
                                        <button
                                            onClick={handleLogout}
                                            className="group flex w-full items-center px-4 py-2 text-sm text-red-600 hover:bg-red-50"
                                        >
                                            <FiLogOut className="mr-3 h-5 w-5 text-red-400 group-hover:text-red-500" />
                                            Logout
                                        </button>
                                    </div>
                                </div>
                            )}
                        </div>
                    </div>
                </div>
            </div>
        </nav>
    )
}

export default UserNav
