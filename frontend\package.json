{"name": "tripy", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@tailwindcss/vite": "^4.1.4", "axios": "^1.9.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-icons": "^5.4.0", "react-redux": "^9.2.0", "react-router-dom": "^7.0.2", "react-toastify": "^11.0.5", "redux": "^5.0.1", "redux-thunk": "^3.1.0", "tailwindcss": "^4.1.4"}, "devDependencies": {"@eslint/js": "^9.15.0", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.15.0", "eslint-plugin-react": "^7.37.2", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.14", "globals": "^15.12.0", "vite": "^6.0.1"}}