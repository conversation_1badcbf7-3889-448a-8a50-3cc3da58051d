import React from 'react';

function Comment({ comment, comments }) {
    const childComments = comments.filter(c => c.parentId === comment.id);

    return (
        <>

            <div className="comment d-flex gap-3 rounded-4 bg-gray  p-3 mb-2">

                <img src={comment.user.profilePic} className='img-fluid profilpic' alt="" />
                <div className="d-flex flex-column justify-content-between">

                    <strong className='text-secondary'>{comment.user.username}</strong>

                    <p>{comment.content}</p>
                </div>


            </div>
            {childComments.length > 0 && (
                <div className="ms-5 subcomment bg-gray rounded-4">
                    {childComments.map((child) => (
                        <Comment key={child.id} comment={child} comments={comments} />
                    ))}
                </div>
            )}
        </>
    );
}

function CommentSection({ comments }) {
    const rootComments = comments.filter(comment => comment.parentId === null);

    return (
        <div className="comment-section my-2">
            {rootComments.map((comment) => (
                <Comment key={comment.id} comment={comment} comments={comments} />
            ))}
        </div>
    );
}

export default CommentSection;
