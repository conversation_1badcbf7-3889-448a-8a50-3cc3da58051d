import { useEffect, useRef } from "react"

// Custom hook for handling click outside
const useClickOutside = (handler) => {
    const ref = useRef()

    useEffect(() => {
        const handleClickOutside = (event) => {
            if (ref.current && !ref.current.contains(event.target)) {
                handler()
            }
        }

        document.addEventListener('mousedown', handleClickOutside)
        return () => {
            document.removeEventListener('mousedown', handleClickOutside)
        }
    }, [handler])

    return ref
}

export default useClickOutside;