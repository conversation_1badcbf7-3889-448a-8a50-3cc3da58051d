@import "tailwindcss";
:root {
  --primary-light: #E0F7FA;
  --primary: #80DEEA;
  --secondary: #0288D1;
  --secondary-dark: #01579B;
  --gray: #F2F2F2;
}

/* :root {
  --primary-light: #E8F5E9;  
  --primary: #81C784;         
  --secondary: #388E3C;       
  --secondary-dark: #1B5E20;  
}
:root {
  --primary-light: #FFE4C4;   
  --primary: #FFB74D;         
  --secondary: #FB8C00;       
  --secondary-dark: #E65100;  
}
:root {
  --primary-light: #FFF8E1;   
  --primary: #FFE0B2;         
  --secondary: #FFCC80;       
  --secondary-dark: #D7CCC8;  
}
:root {
  --primary-light: #F3E5F5;   
  --primary: #CE93D8;         
  --secondary: #8E44AD;       
  --secondary-dark: #4A235A;  
}
:root {
  --primary-light: #FFEBEE;   
  --primary: #FF8A80;         
  --secondary: #E57373;       
  --secondary-dark: #C62828;  
}
:root {
  --primary-light: #FFF9C4;   
  --primary: #FFF176;         
  --secondary: #FFD54F;       
  --secondary-dark: #FFA000; 
} */

.w-64 {
  width: 287px;
}

.ml-64 {
  margin-left: 287px;
}

.logo-md {
  width: 180px;
  height: auto
}

.text-primary {
  color: var(--primary) !important;
}

.text-secondary {
  color: var(--secondary) !important;
}

.active {
  color: var(--secondary) !important;
  background-color: var(--gray) !important;
  font-weight: 600;
}

.border-gray {
  border-color: var(--gray) !important;
}

.bg-secondary {
  background-color: var(--secondary) !important;
}

.bg-primary {
  background-color: var(--primary) !important;
}

.bg-secondary {
  background-color: var(--secondary) !important;
}

.bg-secondary-dark {
  background-color: var(--secondary-dark) !important;
}

.bg-primary-light {
  background-color: var(--primary-light) !important;
}

.bg-gray {
  background-color: var(--gray) !important;
}

.add_button {
  transition: all 0.3s ease-in-out;
}

.add_button:hover {
  background-color: var(--secondary-dark) !important;
  transform: scale(1.05);
}

.shadow-sm:hover {
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.3) !important;
}

.cur-pointer {
  cursor: pointer;

}

.fs-10 {
  font-size: 16px;
}

.fs-12 {
  font-size: 13px;
}

.fs-14 {
  font-size: 10px;
}

.dropdown:hover>.dropdown-menu {
  display: block;
}

.dropdown>.dropdown-toggle:active {
  /*Without this, clicking will make it sticky*/
  pointer-events: none;
}

.aspect-square {
  aspect-ratio: 1 / 1;
}

.w-40 {
  width: 40px !important;
  height: 40px !important;
}

.profilpic {
  border-radius: 50%;
  object-fit: cover;
  width: 40px;
  height: 40px;
}

.post-media {
  height: 400px !important;
  object-fit: cover;
  object-position: center;
}

.fibonacci-gallery {
  display: grid;
  gap: 5px;
}

.single-image {
  width: 100%;
  height: auto;
  border-radius: 8px;
}

.two-images {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 5px;
}

.two-images img {
  width: 100%;
  height: auto;
  border-radius: 8px;
}

.three-images {
  display: grid;
  grid-template-rows: 2fr 1fr;
  grid-template-columns: 1fr 1fr;
  grid-template-areas:
    "main main"
    "side1 side2";
  gap: 5px;
}

.three-images .main-image {
  grid-area: main;
  width: 100%;
  height: auto;
  border-radius: 8px;
}

.three-images .side-images img {
  width: 100%;
  height: auto;
  border-radius: 8px;
}

.four-or-more-images {
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-template-rows: 1fr 1fr;
  gap: 5px;
  position: relative;
}

.four-or-more-images img {
  width: 100%;
  height: auto;
  border-radius: 8px;
}

.overlay {
  position: absolute;
  bottom: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.6);
  color: white;
  font-size: 1.5rem;
  font-weight: bold;
  padding: 10px;
  border-radius: 8px;
}

.subcomment {
  position: relative;
}

.subcomment::before {
  position: absolute;
  content: "";
  width: 20px;
  height: 50px;
  top: -10px;
  right: 100%;
  border-bottom: 5px solid #3498db;
  /* Outline color and thickness */
  border-left: 5px solid #3498db;
  /* Outline color and thickness */
  border-bottom-left-radius: 8px;
}
/* Grid layout for image previews */
#preview {
  display: grid;
  grid-template-columns: repeat(3, 1fr); /* 3 images per row */
  gap: 16px; /* space between images */
}

.previewImage {
  position: relative;
  width: 100%;
  height: 100%;
}

.previewImage img {
  width: 100%;
  height: auto;
  border-radius: 8px;
}

.deleteButton {
  position: absolute;
  top: 4px;
  right: 4px;
  background: rgba(0, 0, 0, 0.5);
  color: white;
  border: none;
  border-radius: 50%;
  padding: 5px;
  cursor: pointer;
  display: flex;
  justify-content: center;
  align-items: center;
}

.deleteButton:hover {
  background: rgba(0, 0, 0, 0.7);
}

.deleteButton:focus {
  outline: none;
}
