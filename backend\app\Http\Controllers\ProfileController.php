<?php

namespace App\Http\Controllers;

use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;

class ProfileController extends Controller
{
    public function updateProfile(Request $request)
    {
        $user = Auth::user();
        
        $validator = Validator::make($request->all(), [
            'nom' => 'sometimes|string|max:255',
            'prenom' => 'sometimes|string|max:255',
            'bio' => 'sometimes|string|max:500',
            'location' => 'sometimes|string|max:255',
            'website' => 'sometimes|url|max:255',
            'date_naissence' => 'sometimes|date|nullable',
            'sex' => 'sometimes|in:homme,femme',
            'profil' => 'sometimes|image|max:2048',
            'cover_image' => 'sometimes|image|max:2048',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $data = $request->only(['nom', 'prenom', 'bio', 'location', 'website', 'date_naissence', 'sex']);

        // Handle profile image upload
        if ($request->hasFile('profil')) {
            // Delete old profile image if exists
            if ($user->profil && !str_starts_with($user->profil, 'http')) {
                Storage::disk('public')->delete($user->profil);
            }
            $profilePath = $request->file('profil')->store('profile_pic', 'public');
            $data['profil'] = $profilePath;
        }

        // Handle cover image upload
        if ($request->hasFile('cover_image')) {
            // Delete old cover image if exists
            if ($user->cover && !str_starts_with($user->cover, 'http')) {
                Storage::disk('public')->delete($user->cover);
            }
            $coverPath = $request->file('cover_image')->store('cover_images', 'public');
            $data['cover'] = $coverPath;
        }

        $user->update($data);

        return response()->json([
            'message' => 'Profile updated successfully',
            'user' => $user
        ]);
    }

    public function getProfile()
    {
        $user = Auth::user();

        // Load user with posts and relationships
        $user->load(['posts.images', 'posts.comments']);

        // Add computed fields
        $user->posts_count = $user->posts()->count();
        $user->saved_posts_count = $user->savedPosts()->count();
        $user->followers_count = $user->followers()->count();
        $user->following_count = $user->following()->count();

        // Add profile and cover image URLs
        $user->profil_url = $user->profil_url;
        $user->cover_url = $user->cover_url;

        return response()->json($user);
    }

    public function getSavedPosts()
    {
        $user = Auth::user();
        $savedPosts = $user->savedPosts()->with(['user', 'images', 'tags'])->get();

        return response()->json($savedPosts);
    }

    public function toggleSavePost(Request $request, $postId)
    {
        $user = Auth::user();

        // Check if post exists
        $post = \App\Models\Post::findOrFail($postId);

        // Check if already saved
        $savedPost = $user->savedPosts()->where('post_id', $postId)->first();

        if ($savedPost) {
            // Unsave the post
            $user->savedPosts()->detach($postId);
            return response()->json([
                'message' => 'Post unsaved successfully',
                'saved' => false
            ]);
        } else {
            // Save the post
            $user->savedPosts()->attach($postId);
            return response()->json([
                'message' => 'Post saved successfully',
                'saved' => true
            ]);
        }
    }

    public function followUser(Request $request, $userId)
    {
        $currentUser = Auth::user();

        if ($currentUser->id == $userId) {
            return response()->json(['message' => 'You cannot follow yourself'], 400);
        }

        $userToFollow = User::findOrFail($userId);

        // Check if already following
        if ($currentUser->following()->where('followed_id', $userId)->exists()) {
            return response()->json(['message' => 'Already following this user'], 400);
        }

        $currentUser->following()->attach($userId);

        return response()->json([
            'message' => 'User followed successfully',
            'following' => true
        ]);
    }

    public function unfollowUser(Request $request, $userId)
    {
        $currentUser = Auth::user();

        $currentUser->following()->detach($userId);

        return response()->json([
            'message' => 'User unfollowed successfully',
            'following' => false
        ]);
    }

    public function getFollowers()
    {
        $user = Auth::user();
        $followers = $user->followers()->with(['posts'])->get();

        return response()->json($followers);
    }

    public function getFollowing()
    {
        $user = Auth::user();
        $following = $user->following()->with(['posts'])->get();

        return response()->json($following);
    }
}