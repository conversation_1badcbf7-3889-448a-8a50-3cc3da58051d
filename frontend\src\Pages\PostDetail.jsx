import { useParams, useNavigate } from 'react-router-dom';
import { useSelector } from 'react-redux';
import UserNav from "../Components/UserNav";
import { MdPlace, MdArrowBack } from "react-icons/md";
import { FaRegHeart, FaRegComment } from "react-icons/fa";
import { IoBookmarkOutline, IoBookmark } from "react-icons/io5";
import CommentSection from '../Components/Comment';
import FibonacciGallery from '../Components/FibonacciGallery';
import { useDispatch } from 'react-redux';
import { toggleSavePost } from '../Reducer/Actions';
import api from '../services/api';
import React, { useState, useEffect } from 'react';
import Image from '../Components/image';

const PostDetail = () => {
    const { postId } = useParams();
    const [post, setPost] = useState(null);
    const navigate = useNavigate();
    const dispatch = useDispatch();

    useEffect(() => {
        console.log(postId)
        api.get(`/posts/${postId}`)
            .then(res => setPost(res.data))
            .catch(err => console.error(err));
    }, [postId]);


    const savedPosts = useSelector(state => state.user.savedPosts);
    const isSaved = false;

    if (!post) {
        return (
            <div className="min-h-screen bg-gray">
                <UserNav />
                <div className="max-w-4xl mx-auto px-4 py-8">
                    <div className="bg-white rounded-xl p-8 text-center">
                        <h1 className="text-2xl font-bold text-gray-900">Post not found</h1>
                        <button
                            onClick={() => navigate(-1)}
                            className="mt-4 inline-flex items-center text-secondary hover:text-secondary-dark"
                        >
                            <MdArrowBack className="mr-2" /> Go back
                        </button>
                    </div>
                </div>
            </div>
        );
    }

    const handleSave = () => {
        dispatch(toggleSavePost(post.id));
    };

    return (
        <div className="min-h-screen bg-gray">
            <UserNav />
            <div className="max-w-4xl mx-auto px-4 py-8">
                <button
                    onClick={() => navigate(-1)}
                    className="mb-6 inline-flex items-center text-gray-600 hover:text-gray-900"
                >
                    <MdArrowBack className="mr-2" /> Back
                </button>

                <div className="bg-white rounded-xl shadow-lg overflow-hidden">
                    {/* Post Header */}
                    <div className="p-6 border-b">
                        <div className="flex items-center justify-between">
                            <div className="flex items-center space-x-4">
                                <Image
                                    src={post.user.profil}
                                    alt={post.user.nom}
                                    className="w-12 h-12 rounded-full object-cover"
                                />
                                <div>
                                    <h2 className="font-semibold text-lg">{post.user.nom}</h2>
                                    <p className="text-gray-500 text-sm">@{post.user.username}</p>
                                </div>
                            </div>
                            <div className="flex items-center space-x-4">
                                <a
                                    href={post.map_url}
                                    target="_blank"
                                    rel="noopener noreferrer"
                                    className="flex items-center space-x-2 text-gray-500 hover:text-gray-700"
                                >
                                    <MdPlace className="text-xl" />
                                    <span>{post.location}</span>
                                </a>
                                <button
                                    onClick={handleSave}
                                    className={`p-2 rounded-full transition-colors duration-200 ${isSaved ? 'text-secondary' : 'text-gray-500 hover:text-secondary'
                                        }`}
                                >
                                    {isSaved ?
                                        <IoBookmark className="w-6 h-6" /> :
                                        <IoBookmarkOutline className="w-6 h-6" />
                                    }
                                </button>
                            </div>
                        </div>
                    </div>

                    {/* Post Content */}
                    <div className="p-6">
                        <p className="text-gray-700 text-lg mb-6">{post.description}</p>

                        {/* Media Gallery */}
                        <div className="mb-6">
                            {post.images.length > 0 &&
                            <FibonacciGallery images={post.images} />
                            }
                        </div>

                        {/* Hashtags */}
                        <div className="flex flex-wrap gap-2 mb-6">
                            {post.tags.map((tag) => (
                                <span
                                    key={tag.id}
                                    className="bg-gray px-3 py-1 rounded-full text-sm text-gray-600"
                                >
                                    #{tag.tag}
                                </span>
                            ))}
                        </div>

                        {/* Engagement Stats */}
                        <div className="flex items-center space-x-6 text-gray-500">
                            <div className="flex items-center space-x-2">
                                <FaRegHeart className="w-6 h-6" />
                                <span>{post.likes}</span>
                            </div>
                            <div className="flex items-center space-x-2">
                                <FaRegComment className="w-6 h-6" />
                                <span>{post.comments?.length || 0}</span>
                            </div>
                            <span className="text-sm">
                                {new Date(post.created_at).toLocaleString(undefined, {
                                    year: 'numeric',
                                    month: 'short',
                                    day: 'numeric',
                                    hour: '2-digit',
                                    minute: '2-digit'
                                })}
                            </span>
                        </div>
                    </div>

                    {/* Comments Section */}
                    <div className="border-t">
                        <div className="p-6">
                            <h3 className="text-xl font-semibold mb-4">Comments</h3>
                            <CommentSection comments={post.comments || []} />
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default PostDetail;