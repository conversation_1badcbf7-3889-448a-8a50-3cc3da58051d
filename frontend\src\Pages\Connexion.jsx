import React, { useState, useEffect } from 'react';
import '../assets/css/Connexion.css';
import { useDispatch } from 'react-redux';
import { login, loginSuccess, register } from '../Reducer/Actions';
import { useNavigate } from 'react-router-dom';
import { toast } from 'react-toastify';
import api from '../services/api';

const Connexion = () => {
    const navigate = useNavigate();
    const dispatch = useDispatch();
    const [isSignIn, setIsSignIn] = useState(true);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);
    
    // Login form state
    const [loginData, setLoginData] = useState({
        email: '',
        password: ''
    });
    
    // Register form state
    const [registerData, setRegisterData] = useState({
        username: '',
        nom: '',
        prenom: '',
        email: '',
        password: '',
        password_confirmation: ''
    });

    const toggle = () => {
        setIsSignIn(!isSignIn);
        setError(null);
    };

    const handleLoginChange = (e) => {
        setLoginData({
            ...loginData,
            [e.target.name]: e.target.value
        });
    };

    const handleRegisterChange = (e) => {
        setRegisterData({
            ...registerData,
            [e.target.name]: e.target.value
        });
    };

    const handleLogin = async (e) => {
        e.preventDefault();
        setLoading(true);
        setError(null);
        
        console.log('Login form submitted', loginData);
        
        try {
            const response = await api.post("/login", loginData);
            const userData = response.data;
            
            // Store token in localStorage
            localStorage.setItem('token', userData.token);
            localStorage.setItem('user', JSON.stringify(userData.user));
            
            // Dispatch login action
            dispatch(loginSuccess(userData.user));
            
            toast.success('Login successful!');
            console.log('Login successful, navigating to home');
            navigate('/');
        } catch (err) {
            console.error('Login error in component:', err);
            let errorMessage = 'Login failed';
            
            if (err.response) {
                errorMessage = err.response.data.message || errorMessage;
                console.error('Error response:', err.response.data);
            } else if (err.request) {
                console.error('No response received:', err.request);
                errorMessage = 'No response from server. Please check your connection.';
            } else {
                console.error('Error details:', err);
                errorMessage = err.message || errorMessage;
            }
            
            toast.error(errorMessage);
            setError(errorMessage);
        } finally {
            setLoading(false);
        }
    };

    const handleRegister = async (e) => {
        e.preventDefault();
        setLoading(true);
        setError(null);
        
        console.log('Register form submitted', registerData);
        
        if (registerData.password !== registerData.password_confirmation) {
            const errorMsg = 'Passwords do not match';
            toast.error(errorMsg);
            setError(errorMsg);
            setLoading(false);
            return;
        }
        
        try {
            const response = await api.post("/register", registerData);
            const userData = response.data;
            
            // Store token in localStorage
            localStorage.setItem('token', userData.token);
            localStorage.setItem('user', JSON.stringify(userData.user));
            
        
            dispatch(loginSuccess(userData.user));
            
            toast.success('Registration successful!');
            console.log('Registration successful, navigating to home');
            navigate('/');
        } catch (err) {
            console.error('Register error in component:', err);
            let errorMessage = 'Registration failed';
            
            if (err.response) {
                if (err.response.data.errors) {
                    const errors = err.response.data.errors;
                    console.error('Validation errors:', errors);
                    const firstError = Object.values(errors)[0];
                    errorMessage = Array.isArray(firstError) ? firstError[0] : firstError;
                } else if (err.response.data.message) {
                    errorMessage = err.response.data.message;
                }
            } else if (err.request) {
                console.error('No response received:', err.request);
                errorMessage = 'No response from server. Please check your connection.';
            } else {
                console.error('Error details:', err);
                errorMessage = err.message || errorMessage;
            }
            
            toast.error(errorMessage);
            setError(errorMessage);
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        const container = document.getElementById('container');
        if (isSignIn) {
            container.classList.add('sign-in');
            container.classList.remove('sign-up');
        } else {
            container.classList.add('sign-up');
            container.classList.remove('sign-in');
        }
    }, [isSignIn]);

    return (
       <section>
         <div id="container" className="conn-container">
            <div className="row2">
                <div className="col2 align-items-center flex-col sign-up">
                    <div className="form-wrapper align-items-center">
                        <div className="form sign-up">
                            <form onSubmit={handleRegister}>
                                <div className="input-group">
                                    <i className='bx bxs-user'></i>
                                    <input 
                                        type="text" 
                                        name="username" 
                                        placeholder="Username" 
                                        value={registerData.username}
                                        onChange={handleRegisterChange}
                                        required
                                    />
                                </div>
                                <div className="input-group">
                                    <i className='bx bxs-user'></i>
                                    <input 
                                        type="text" 
                                        name="nom" 
                                        placeholder="Last Name" 
                                        value={registerData.nom}
                                        onChange={handleRegisterChange}
                                        required
                                    />
                                </div>
                                <div className="input-group">
                                    <i className='bx bxs-user'></i>
                                    <input 
                                        type="text" 
                                        name="prenom" 
                                        placeholder="First Name" 
                                        value={registerData.prenom}
                                        onChange={handleRegisterChange}
                                        required
                                    />
                                </div>
                                <div className="input-group">
                                    <i className='bx bx-mail-send'></i>
                                    <input 
                                        type="email" 
                                        name="email" 
                                        placeholder="Email" 
                                        value={registerData.email}
                                        onChange={handleRegisterChange}
                                        required
                                    />
                                </div>
                                <div className="input-group">
                                    <i className='bx bxs-lock-alt'></i>
                                    <input 
                                        type="password" 
                                        name="password" 
                                        placeholder="Password" 
                                        value={registerData.password}
                                        onChange={handleRegisterChange}
                                        required
                                    />
                                </div>
                                <div className="input-group">
                                    <i className='bx bxs-lock-alt'></i>
                                    <input 
                                        type="password" 
                                        name="password_confirmation" 
                                        placeholder="Confirm password" 
                                        value={registerData.password_confirmation}
                                        onChange={handleRegisterChange}
                                        required
                                    />
                                </div>
                                <button type="submit" disabled={loading}>
                                    {loading ? 'Signing up...' : 'Sign up'}
                                </button>
                            </form>
                            <p>
                                <span>
                                    Already have an account?
                                </span>
                                <b onClick={toggle} className="pointer">
                                    Sign in here
                                </b>
                            </p>
                        </div>
                    </div>
                </div>
                <div className="col2 align-items-center flex-col sign-in">
                    <div className="form-wrapper align-items-center">
                        <div className="form sign-in">
                            <form onSubmit={handleLogin}>
                                <div className="input-group">
                                    <i className='bx bx-mail-send'></i>
                                    <input 
                                        type="email" 
                                        name="email" 
                                        placeholder="Email" 
                                        value={loginData.email}
                                        onChange={handleLoginChange}
                                        required
                                    />
                                </div>
                                <div className="input-group">
                                    <i className='bx bxs-lock-alt'></i>
                                    <input 
                                        type="password" 
                                        name="password" 
                                        placeholder="Password" 
                                        value={loginData.password}
                                        onChange={handleLoginChange}
                                        required
                                    />
                                </div>
                                <button type="submit" disabled={loading}>
                                    {loading ? 'Signing in...' : 'Sign in'}
                                </button>
                            </form>
                            <p>
                                <span>
                                    Don't have an account?
                                </span>
                                <b onClick={toggle} className="pointer">
                                    Sign up here
                                </b>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
            <div className="row2 content-row">
                <div className="col2 align-items-center flex-col">
                    <div className="text sign-in">
                        <h2>
                            Welcome
                        </h2>
                    </div>
                    <div className="img sign-in"></div>
                </div>

                <div className="col2 align-items-center flex-col">
                    <div className="img sign-up"></div>
                    <div className="text sign-up">
                        <h2>
                            Join with us
                        </h2>
                    </div>
                </div>
            </div>
        </div>
       </section>
    );
};

export default Connexion;
