import React, { useState, useEffect, useRef } from 'react';
import { FaImage, FaTimes, FaMapMarkerAlt } from 'react-icons/fa';
import { IoTrendingUp } from 'react-icons/io5';
import api from '../services/api';
import { toast } from 'react-toastify';
import { useSelector } from 'react-redux';

const SharePost = () => {
    const [content, setContent] = useState('');
    const [media, setMedia] = useState([]);
    const [title, setTitle] = useState('');
    const [place, setPlace] = useState('');
    const [hashtag, setHashtag] = useState('');
    const [hashtags, setHashtags] = useState([]);
    const [loading, setLoading] = useState(false);
    const [availableTags, setAvailableTags] = useState([]);
    const [tagSuggestions, setTagSuggestions] = useState([]);
    const [showSuggestions, setShowSuggestions] = useState(false);
    const fileInputRef = useRef(null);
    const user = useSelector(state => state.user);
    const trendingTags = useSelector(state => state.trendingTags);

    // Helper function to check if a tag is trending
    const isTagTrending = (tagName) => {
        return trendingTags.data.some(trendingTag =>
            (trendingTag.tag || trendingTag.title) === tagName
        );
    };

    // Generate map URL based on place name
    const mapUrl = place ? `https://www.google.com/maps/search/?api=1&query=${encodeURIComponent(place)}` : '';

    // Fetch available tags on component mount
    useEffect(() => {
        const fetchTags = async () => {
            try {
                const response = await api.get('/post');
                setAvailableTags(response.data || []);
            } catch (error) {
                console.error('Error fetching tags:', error);
            }
        };
        
        fetchTags();
    }, []);

    // Filter tag suggestions based on input
    useEffect(() => {
        if (hashtag.trim()) {
            const filtered = availableTags
                .filter(tag => tag.tag?.toLowerCase().includes(hashtag.toLowerCase()))
                .slice(0, 5);
            setTagSuggestions(filtered);
            setShowSuggestions(filtered.length > 0);
        } else {
            setTagSuggestions([]);
            setShowSuggestions(false);
        }
    }, [hashtag, availableTags]);

    const handleImageUpload = (e) => {
        const files = Array.from(e.target.files);
        
        // Create objects with file and preview URL
        const newMedia = files.map(file => ({
            file,
            preview: URL.createObjectURL(file),
            id: Math.random().toString(36).substring(2)
        }));
        
        setMedia([...media, ...newMedia]);
        
        // Reset file input
        if (fileInputRef.current) {
            fileInputRef.current.value = '';
        }
    };

    const handleRemoveImage = (id) => {
        const updatedMedia = media.filter(item => item.id !== id);
        setMedia(updatedMedia);
    };

    const handleAddTag = (tag) => {
        if (tag && !hashtags.includes(tag)) {
            setHashtags([...hashtags, tag]);
            setHashtag('');
            setShowSuggestions(false);
        }
    };

    const handleRemoveTag = (tag) => {
        setHashtags(hashtags.filter(t => t !== tag));
    };

    const handleTagKeyDown = (e) => {
        if (e.key === 'Enter' && hashtag.trim()) {
            e.preventDefault();
            handleAddTag(hashtag.trim());
        } else if (e.key === 'Backspace' && !hashtag && hashtags.length > 0) {
            handleRemoveTag(hashtags[hashtags.length - 1]);
        }
    };

    const handleSubmit = async () => {
        if (!content.trim()) {
            toast.error('Please add some content to your post');
            return;
        }

        setLoading(true);

        try {
            const formData = new FormData();
            formData.append('description', content);
            formData.append('localisation', place);
            
            // Add tags
            hashtags.forEach(tag => {
                formData.append('tags[]', tag);
            });
            
            // Add images
            media.forEach(item => {
                formData.append('images[]', item.file);
            });

            const response = await api.post('/posts', formData, {
                headers: {
                    'Content-Type': 'multipart/form-data'
                }
            });

            toast.success('Post created successfully!');
            
            // Reset form
            setContent('');
            setMedia([]);
            setTitle('');
            setPlace('');
            setHashtags([]);
            setHashtag('');
            
        } catch (error) {
            console.error('Error creating post:', error);
            toast.error('Failed to create post. Please try again.');
        } finally {
            setLoading(false);
        }
    };

    const openMapPreview = () => {
        if (place) {
            window.open(mapUrl, '_blank');
        }
    };

    return (
        <div className="w-full px-4 py-10 flex justify-center">
            <div className="w-full max-w-6xl bg-white p-8 rounded-2xl shadow-lg space-y-6">
                <h2 className="text-3xl font-bold text-gray-800 flex items-center gap-2">
                    <span className="text-secondary">📍</span> Share Your Journey
                </h2>

                {/* Title input */}
                <div className="relative">
                    <input
                        type="text"
                        placeholder="Title your adventure..."
                        value={title}
                        onChange={(e) => setTitle(e.target.value)}
                        className="w-full text-lg p-4 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-secondary/50 shadow-sm"
                    />
                </div>

                {/* Content textarea */}
                <div className="relative">
                    <textarea
                        placeholder="Tell us about your experience..."
                        value={content}
                        onChange={(e) => setContent(e.target.value)}
                        className="w-full text-lg p-4 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-secondary/50 resize-none h-40 shadow-sm"
                    />
                </div>

                {/* Location input */}
                <div className="relative">
                    <div className="flex items-center p-2 bg-gray-50 rounded-xl border border-gray-200">
                        <FaMapMarkerAlt className="text-secondary ml-2 mr-2" />
                        <input
                            type="text"
                            placeholder="Where did you go? (e.g., Eiffel Tower, Paris)"
                            value={place}
                            onChange={(e) => setPlace(e.target.value)}
                            className="flex-1 p-2 bg-transparent border-none focus:outline-none focus:ring-0"
                        />
                        {place && (
                            <button
                                type="button"
                                onClick={openMapPreview}
                                className="px-3 py-1 bg-secondary/10 text-secondary rounded-lg hover:bg-secondary/20 transition-colors ml-2"
                            >
                                Preview Map
                            </button>
                        )}
                    </div>
                </div>
                
                {/* Hashtags section */}
                <div className="space-y-2">
                    <div className="relative">
                        <div className="flex items-center p-2 bg-gray-50 rounded-xl border border-gray-200">
                            <span className="text-secondary ml-2 mr-2">#</span>
                            <input
                                type="text"
                                placeholder="Add hashtags... (Enter after each one)"
                                value={hashtag}
                                onChange={(e) => setHashtag(e.target.value)}
                                onKeyDown={handleTagKeyDown}
                                className="flex-1 p-2 bg-transparent border-none focus:outline-none focus:ring-0"
                            />
                        </div>
                        
                        {showSuggestions && (
                            <div className="absolute z-10 w-full mt-1 bg-white rounded-xl shadow-lg border border-gray-200">
                                {tagSuggestions.map(tag => {
                                    const isTrending = isTagTrending(tag.tag);
                                    return (
                                        <div
                                            key={tag.id}
                                            onClick={() => handleAddTag(tag.tag)}
                                            className={`p-2 hover:bg-gray-50 cursor-pointer flex items-center justify-between ${
                                                isTrending ? 'bg-secondary/5' : ''
                                            }`}
                                        >
                                            <span className={isTrending ? 'text-secondary font-medium' : ''}>
                                                #{tag.tag}
                                            </span>
                                            {isTrending && (
                                                <div className="flex items-center gap-1 text-secondary text-xs">
                                                    <IoTrendingUp className="w-3 h-3" />
                                                    <span>Trending</span>
                                                </div>
                                            )}
                                        </div>
                                    );
                                })}
                            </div>
                        )}
                    </div>
                    
                    {hashtags.length > 0 && (
                        <div className="flex flex-wrap gap-2 mt-2">
                            {hashtags.map(tag => (
                                <span 
                                    key={tag} 
                                    className="px-3 py-1 bg-secondary/10 text-secondary rounded-full flex items-center gap-1"
                                >
                                    #{tag}
                                    <button 
                                        onClick={() => handleRemoveTag(tag)}
                                        className="hover:text-secondary-dark"
                                    >
                                        <FaTimes size={12} />
                                    </button>
                                </span>
                            ))}
                        </div>
                    )}
                </div>

                {/* Media upload section */}
                <div className="space-y-4">
                    <div 
                        onClick={() => fileInputRef.current.click()}
                        className="border-2 border-dashed border-gray-300 rounded-xl p-8 flex flex-col items-center justify-center cursor-pointer hover:border-secondary/50 transition-colors"
                    >
                        <FaImage className="text-4xl text-gray-400 mb-2" />
                        <p className="text-gray-500">Click to upload images</p>
                        <p className="text-xs text-gray-400 mt-1">JPG, PNG, GIF up to 10MB</p>
                        <input
                            type="file"
                            multiple
                            accept="image/*"
                            onChange={handleImageUpload}
                            ref={fileInputRef}
                            className="hidden"
                        />
                    </div>

                    {media.length > 0 && (
                        <div id="preview" className="grid grid-cols-3 gap-4">
                            {media.map(item => (
                                <div key={item.id} className="previewImage relative aspect-square rounded-xl overflow-hidden bg-gray-100">
                                    <img 
                                        src={item.preview} 
                                        alt="Preview" 
                                        className="w-full h-full object-cover"
                                    />
                                    <button
                                        onClick={() => handleRemoveImage(item.id)}
                                        className="deleteButton absolute top-2 right-2 bg-black/60 text-white p-1.5 rounded-full hover:bg-black/80 transition-colors"
                                    >
                                        <FaTimes size={14} />
                                    </button>
                                </div>
                            ))}
                        </div>
                    )}
                </div>

                {/* Submit button */}
                <div className="pt-4">
                    <button
                        onClick={handleSubmit}
                        disabled={loading}
                        className="w-full py-4 bg-secondary hover:bg-secondary-dark text-white rounded-xl font-semibold transition-colors shadow-sm flex items-center justify-center gap-2"
                    >
                        {loading ? (
                            <>
                                <svg className="animate-spin h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                </svg>
                                <span>Posting...</span>
                            </>
                        ) : (
                            <span>Share Your Journey</span>
                        )}
                    </button>
                </div>
            </div>
        </div>
    );
};

export default SharePost;
