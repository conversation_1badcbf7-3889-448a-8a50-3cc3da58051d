import { useSelector } from "react-redux";
import UserNav from "../Components/UserNav";
import Post from "../Components/Post";
import { useEffect, useState } from "react";
import SharePost from "../Components/SharePost";
import api from "../services/api";

const Home = () => {
    const [posts, setPosts] = useState([]);



    useEffect(()=>{
        api.get("/posts").then((res)=>{
            setPosts(res.data)
        })
    },[])

    return (
        <section className="min-h-screen bg-gray">
            <UserNav />
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
                <div className="flex  flex-nowrap lg:flex-row gap-6">
                    <div className="lg:w-2/3 mx-auto">
                        <div className="space-y-6">
                            {posts.map((post) => (
                                <Post key={post.id} post={post} />
                            ))}
                        </div>
                    </div>
                    
                </div>
            </div>
        </section>
    )
}

export default Home;
